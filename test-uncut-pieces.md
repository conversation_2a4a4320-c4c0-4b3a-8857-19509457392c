# Test Plan for Uncut Pieces Feature

## Test Scenario
To test the uncut pieces functionality, create a project with the following setup:

### Materials (Small sheets):
1. **Plywood Sheet 1**
   - Dimensions: 600mm × 400mm
   - Quantity: 2
   - Unit: MM

### Pieces (Some too large):
1. **Small Piece 1**
   - Dimensions: 200mm × 150mm
   - Quantity: 2
   - Unit: MM

2. **Small Piece 2**
   - Dimensions: 300mm × 200mm
   - Quantity: 1
   - Unit: MM

3. **Large Piece (Too Big)**
   - Dimensions: 800mm × 500mm
   - Quantity: 1
   - Unit: MM

4. **Another Large Piece (Too Big)**
   - Dimensions: 700mm × 600mm
   - Quantity: 2
   - Unit: MM

## Expected Results
When optimization is run:

1. **Small pieces should be cut successfully** on the available 600×400mm sheets
2. **Large pieces should appear as "uncut pieces"** because they exceed the material dimensions
3. **UI should show warnings** in multiple places:
   - Toast notification with warning variant
   - Alert in optimization status (red variant)
   - Warning alert in cutting list view
   - Warning alert in visualization view
   - Uncut pieces section in cutting list with red styling
4. **Print functionality should include** uncut pieces section with warnings
5. **Export text should include** uncut pieces section

## UI Elements to Verify

### Project Detail Page:
- [ ] Toast shows warning with piece count
- [ ] Optimization status alert is red with warning message
- [ ] All tabs show appropriate warnings

### Cutting List Tab:
- [ ] Red warning alert at top
- [ ] Dedicated "Uncut Pieces" section with red styling
- [ ] Helpful suggestions box with yellow styling
- [ ] Export text includes uncut pieces section

### Visualization Tab:
- [ ] Red warning alert mentioning to check cutting list
- [ ] Only successfully cut pieces are shown in visualization

### Print Output:
- [ ] Warning box in header
- [ ] Dedicated uncut pieces section at bottom
- [ ] Suggestions included in print

## API Response Verification
Check that the optimization API returns:
```json
{
  "success": true,
  "layouts": [...],
  "uncutPieces": [
    {
      "id": "...",
      "name": "Large Piece (Too Big)",
      "length": 800,
      "width": 500,
      "unit": "MM",
      "quantity": 1
    },
    {
      "id": "...",
      "name": "Another Large Piece (Too Big)",
      "length": 700,
      "width": 600,
      "unit": "MM",
      "quantity": 2
    }
  ],
  "metadata": {
    "totalPieces": 6,
    "cutPieces": 3,
    "uncutPieces": 3,
    ...
  }
}
```
