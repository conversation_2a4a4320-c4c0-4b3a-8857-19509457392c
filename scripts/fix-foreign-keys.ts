import 'dotenv/config';
import { Client } from 'pg';

async function main() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL!,
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Step 1: Drop the old foreign key constraint that points to users.id
    console.log('\n1. Dropping old foreign key constraint...');
    await client.query(`
      ALTER TABLE projects 
      DROP CONSTRAINT IF EXISTS projects_userId_users_id_fk;
    `);
    console.log('✅ Dropped projects_userId_users_id_fk');

    // Step 2: Add the correct foreign key constraint that points to user.id
    console.log('\n2. Adding correct foreign key constraint...');
    await client.query(`
      ALTER TABLE projects
      ADD CONSTRAINT projects_userId_user_id_fk
      FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE;
    `);
    console.log('✅ Added projects_userId_user_id_fk');

    // Step 3: Fix materials table foreign key constraint
    console.log('\n3. Fixing materials table foreign key constraint...');
    await client.query(`
      ALTER TABLE materials
      DROP CONSTRAINT IF EXISTS materials_userId_users_id_fk;
    `);
    await client.query(`
      ALTER TABLE materials
      ADD CONSTRAINT materials_userId_user_id_fk
      FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE;
    `);
    console.log('✅ Fixed materials foreign key constraint');

    // Step 4: Fix optimization_results table foreign key constraint
    console.log('\n4. Fixing optimization_results table foreign key constraint...');
    await client.query(`
      ALTER TABLE optimization_results
      DROP CONSTRAINT IF EXISTS optimization_results_userId_users_id_fk;
    `);
    await client.query(`
      ALTER TABLE optimization_results
      ADD CONSTRAINT optimization_results_userId_user_id_fk
      FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE;
    `);
    console.log('✅ Fixed optimization_results foreign key constraint');

    // Step 5: Drop the empty users table (optional cleanup)
    console.log('\n5. Dropping empty users table...');
    await client.query(`DROP TABLE IF EXISTS users;`);
    console.log('✅ Dropped empty users table');

    // Step 6: Verify the new constraints
    console.log('\n6. Verifying new constraints...');
    const constraintsQuery = `
      SELECT 
        tc.constraint_name, 
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE 
        tc.constraint_type = 'FOREIGN KEY' 
        AND kcu.column_name = 'userId'
        AND tc.table_name IN ('projects', 'materials', 'optimization_results');
    `;

    const result = await client.query(constraintsQuery);
    console.log('\nUpdated foreign key constraints:');
    result.rows.forEach(row => {
      console.log(`✅ ${row.constraint_name}: ${row.table_name}.${row.column_name} -> ${row.foreign_table_name}.${row.foreign_column_name}`);
    });

    console.log('\n🎉 Foreign key constraints fixed successfully!');

  } catch (error) {
    console.error('❌ Error fixing foreign key constraints:', error);
    process.exit(1);
  } finally {
    await client.end();
    console.log('\nDatabase connection closed');
  }
}

main();
