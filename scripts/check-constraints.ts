import 'dotenv/config';
import { Client } from 'pg';

async function main() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL!,
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Check foreign key constraints on projects table
    const constraintsQuery = `
      SELECT 
        tc.constraint_name, 
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE 
        tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'projects'
        AND kcu.column_name = 'userId';
    `;

    const result = await client.query(constraintsQuery);
    
    console.log('\nForeign key constraints on projects.userId:');
    result.rows.forEach(row => {
      console.log(`- ${row.constraint_name}: projects.${row.column_name} -> ${row.foreign_table_name}.${row.foreign_column_name}`);
    });

    // Check if both user and users tables exist
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('user', 'users');
    `;

    const tablesResult = await client.query(tablesQuery);
    console.log('\nExisting user tables:');
    tablesResult.rows.forEach(row => {
      console.log(`- ${row.table_name}`);
    });

    // Check users in both tables
    try {
      const userTableQuery = 'SELECT COUNT(*) as count FROM "user"';
      const userResult = await client.query(userTableQuery);
      console.log(`\nUsers in "user" table: ${userResult.rows[0].count}`);
    } catch (e) {
      console.log('\n"user" table does not exist or is empty');
    }

    try {
      const usersTableQuery = 'SELECT COUNT(*) as count FROM "users"';
      const usersResult = await client.query(usersTableQuery);
      console.log(`Users in "users" table: ${usersResult.rows[0].count}`);
    } catch (e) {
      console.log('"users" table does not exist or is empty');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.end();
    console.log('\nDatabase connection closed');
  }
}

main();
