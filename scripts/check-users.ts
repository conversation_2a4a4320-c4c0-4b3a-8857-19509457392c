import 'dotenv/config';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Client } from 'pg';
import * as schema from '../src/lib/schema';

async function main() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL!,
  });

  try {
    await client.connect();
    console.log('Connected to database');

    const db = drizzle(client, { schema });

    // Check users in the user table (Better-Auth)
    const users = await db.query.user.findMany();
    console.log('Users in user table:', users.length);
    users.forEach(user => {
      console.log(`- ${user.email} (${user.name}) - ID: ${user.id}`);
    });

    // Check projects
    const projects = await db.query.projects.findMany();
    console.log('\nProjects:', projects.length);
    projects.forEach(project => {
      console.log(`- ${project.name} (User ID: ${project.userId})`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

main();
