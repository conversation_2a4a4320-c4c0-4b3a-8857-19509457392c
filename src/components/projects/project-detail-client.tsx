'use client'

import { useState, useCallback } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ArrowLeft, Zap, FileText, Eye, Plus, Settings } from 'lucide-react'
import Link from 'next/link'
import { useToast } from '@/hooks/use-toast'
import { PieceManager } from './piece-manager'
import { CuttingListView } from './cutting-list-view'
import { VisualizationView } from './visualization-view'

interface ProjectDetailClientProps {
  project: {
    id: string
    name: string
    description: string | null
    saw_kerf: number
    kerf_unit: string
    pieces: Array<{
      id: string
      name: string
      length: number
      width: number
      thickness: number | null
      unit: string
      quantity: number
      grain_direction: string
      priority: number
      notes: string | null
    }>
  }
  materials: Array<{
    id: string
    name: string
    length: number
    width: number
    thickness: number | null
    unit: string
    quantity: number
    cost: number | null
  }>
  initialOptimizationResult: any
}

export function ProjectDetailClient({ 
  project, 
  materials, 
  initialOptimizationResult 
}: ProjectDetailClientProps) {
  const [optimizing, setOptimizing] = useState(false)
  const [optimizationResult, setOptimizationResult] = useState(initialOptimizationResult)
  const [activeTab, setActiveTab] = useState('pieces')
  const { toast } = useToast()

  const handleOptimize = useCallback(async () => {
    if (project.pieces.length === 0) {
      toast({
        title: 'No pieces to optimize',
        description: 'Please add some pieces to your project before optimizing.',
        variant: 'destructive',
      })
      return
    }

    if (materials.length === 0) {
      toast({
        title: 'No materials available',
        description: 'Please add materials to your inventory before optimizing.',
        variant: 'destructive',
      })
      return
    }

    setOptimizing(true)

    try {
      const response = await fetch('/api/optimize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          materials,
          pieces: project.pieces,
          sawKerf: project.saw_kerf,
          kerfUnit: project.kerf_unit,
          projectId: project.id,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Optimization failed')
      }

      if (result.success) {
        setOptimizationResult(result)
        setActiveTab('visualization')

        // Create toast message with uncut pieces warning if any
        let description = `Generated ${result.layouts.length} sheet(s) with ${result.metadata.efficiency.toFixed(1)}% efficiency.`
        if (result.uncutPieces && result.uncutPieces.length > 0) {
          const uncutCount = result.uncutPieces.reduce((sum: number, piece: any) => sum + piece.quantity, 0)
          description += ` Warning: ${uncutCount} piece(s) could not be cut.`
        }

        toast({
          title: 'Optimization complete!',
          description,
          variant: result.uncutPieces && result.uncutPieces.length > 0 ? 'destructive' : 'default',
        })
      } else {
        throw new Error(result.error || 'Optimization failed')
      }
    } catch (error) {
      console.error('Optimization error:', error)
      toast({
        title: 'Optimization failed',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      })
    } finally {
      setOptimizing(false)
    }
  }, [project, materials, toast])

  const handlePrint = useCallback(() => {
    if (!optimizationResult?.layouts || optimizationResult.layouts.length === 0) {
      toast({
        title: 'No cutting plan to print',
        description: 'Please run optimization first.',
        variant: 'destructive',
      })
      return
    }

    // Generate print content
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      // This would generate the print-friendly HTML
      // For now, just show a placeholder
      printWindow.document.write(`
        <html>
          <head>
            <title>${project.name} - Cutting Plan</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
              .sheet { page-break-before: always; margin-bottom: 30px; }
              .piece { margin: 5px 0; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>${project.name} - Cutting Plan</h1>
              <p>Generated: ${new Date().toLocaleDateString()}</p>
              <p>Total Sheets: ${optimizationResult.layouts.length}</p>
              <p>Efficiency: ${optimizationResult.metadata?.efficiency?.toFixed(1) || 'N/A'}%</p>
              ${optimizationResult.uncutPieces && optimizationResult.uncutPieces.length > 0 ? `
                <div style="color: #dc2626; font-weight: bold; margin-top: 10px; padding: 10px; border: 2px solid #dc2626; background-color: #fef2f2;">
                  ⚠️ WARNING: ${optimizationResult.uncutPieces.reduce((sum: number, piece: any) => sum + piece.quantity, 0)} piece(s) could not be cut with available materials
                </div>
              ` : ''}
            </div>
            ${optimizationResult.layouts.map((layout: any, index: number) => `
              <div class="sheet">
                <h2>Sheet ${index + 1}: ${layout.baseMaterial.name}</h2>
                <p>Material: ${layout.widthUsed} × ${layout.heightUsed} ${layout.baseMaterial.unit}</p>
                ${layout.pieces.map((piece: any, pieceIndex: number) => `
                  <div class="piece">
                    ${pieceIndex + 1}. ${piece.name} - ${piece.length} × ${piece.width} ${piece.unit}
                  </div>
                `).join('')}
              </div>
            `).join('')}
            ${optimizationResult.uncutPieces && optimizationResult.uncutPieces.length > 0 ? `
              <div class="sheet" style="border: 2px solid #dc2626; background-color: #fef2f2; padding: 15px;">
                <h2 style="color: #dc2626;">⚠️ UNCUT PIECES</h2>
                <p style="color: #dc2626; font-weight: bold;">The following pieces could not be cut:</p>
                ${optimizationResult.uncutPieces.map((piece: any, index: number) => `
                  <div class="piece" style="color: #dc2626;">
                    ${index + 1}. ${piece.name} - ${piece.length} × ${piece.width} ${piece.unit} (Qty: ${piece.quantity})
                  </div>
                `).join('')}
                <div style="margin-top: 15px; padding: 10px; background-color: #fef3c7; border: 1px solid #f59e0b;">
                  <strong>Possible Solutions:</strong>
                  <ul>
                    <li>Add larger material sheets to inventory</li>
                    <li>Increase quantity of existing materials</li>
                    <li>Split large pieces into smaller components</li>
                    <li>Check if pieces can be rotated (grain direction permitting)</li>
                  </ul>
                </div>
              </div>
            ` : ''}
          </body>
        </html>
      `)
      printWindow.document.close()
      printWindow.print()
    }
  }, [optimizationResult, project.name, toast])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/projects">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
            {project.description && (
              <p className="text-gray-600 mt-1">{project.description}</p>
            )}
            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
              <span>{project.pieces.length} pieces</span>
              <span>Saw kerf: {project.saw_kerf}{project.kerf_unit}</span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleOptimize}
            disabled={optimizing}
            className="bg-green-600 hover:bg-green-700"
          >
            {optimizing ? (
              'Optimizing...'
            ) : (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Optimize
              </>
            )}
          </Button>
          <Button variant="outline" onClick={handlePrint}>
            <FileText className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/dashboard/projects/${project.id}/settings`}>
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Link>
          </Button>
        </div>
      </div>

      {/* Optimization Status */}
      {optimizationResult && (
        <Alert variant={optimizationResult.uncutPieces && optimizationResult.uncutPieces.length > 0 ? 'destructive' : 'default'}>
          <Zap className="h-4 w-4" />
          <AlertDescription>
            Last optimization: {optimizationResult.layouts.length} sheet(s) with{' '}
            {optimizationResult.metadata?.efficiency?.toFixed(1) || 'N/A'}% efficiency
            {optimizationResult.metadata?.processingTime && (
              <> in {optimizationResult.metadata.processingTime}ms</>
            )}
            {optimizationResult.uncutPieces && optimizationResult.uncutPieces.length > 0 && (
              <>
                <br />
                <strong>Warning:</strong> {optimizationResult.uncutPieces.reduce((sum: number, piece: any) => sum + piece.quantity, 0)} piece(s) could not be cut
              </>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="pieces">
            <Plus className="h-4 w-4 mr-2" />
            Pieces
          </TabsTrigger>
          <TabsTrigger value="cutting-list">
            <FileText className="h-4 w-4 mr-2" />
            Cutting List
          </TabsTrigger>
          <TabsTrigger value="visualization">
            <Eye className="h-4 w-4 mr-2" />
            Visualization
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pieces" className="space-y-6">
          <PieceManager projectId={project.id} pieces={project.pieces} />
        </TabsContent>

        <TabsContent value="cutting-list" className="space-y-6">
          <CuttingListView 
            project={project}
            optimizationResult={optimizationResult}
          />
        </TabsContent>

        <TabsContent value="visualization" className="space-y-6">
          <VisualizationView 
            optimizationResult={optimizationResult}
            onOptimize={handleOptimize}
            optimizing={optimizing}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
