"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Plus, Edit, Trash2, Save, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";

interface Piece {
  id: string;
  name: string;
  length: number;
  width: number;
  thickness: number | null;
  unit: string;
  quantity: number;
  grain_direction: string;
  priority: number;
  notes: string | null;
}

interface PieceManagerProps {
  projectId: string;
  pieces: Piece[];
}

export function PieceManager({
  projectId,
  pieces: initialPieces,
}: PieceManagerProps) {
  const [pieces, setPieces] = useState(initialPieces);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    length: "",
    width: "",
    thickness: "18.0",
    unit: "MM",
    quantity: "1",
    grainDirection: "NONE",
    priority: "1",
    notes: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const router = useRouter();
  const { toast } = useToast();

  const resetForm = () => {
    setFormData({
      name: "",
      length: "",
      width: "",
      thickness: "18.0",
      unit: "MM",
      quantity: "1",
      grainDirection: "NONE",
      priority: "1",
      notes: "",
    });
    setEditingId(null);
    setError("");
  };

  const handleEdit = (piece: Piece) => {
    setFormData({
      name: piece.name,
      length: piece.length.toString(),
      width: piece.width.toString(),
      thickness: piece.thickness?.toString() || "",
      unit: piece.unit,
      quantity: piece.quantity.toString(),
      grainDirection: piece.grain_direction,
      priority: piece.priority.toString(),
      notes: piece.notes || "",
    });
    setEditingId(piece.id);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    if (!formData.name.trim() || !formData.length || !formData.width) {
      setError("Name, length, and width are required");
      setLoading(false);
      return;
    }

    try {
      const pieceData = {
        name: formData.name.trim(),
        length: parseFloat(formData.length),
        width: parseFloat(formData.width),
        thickness: formData.thickness
          ? parseFloat(formData.thickness)
          : undefined,
        unit: formData.unit,
        quantity: parseInt(formData.quantity),
        grainDirection: formData.grainDirection,
        priority: parseInt(formData.priority),
        notes: formData.notes.trim() || null,
        projectId: projectId,
      };

      if (editingId) {
        // Update existing piece
        const response = await fetch(`/api/pieces/${editingId}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(pieceData),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || "Failed to update piece");
        }

        setPieces((prev) =>
          prev.map((piece) =>
            piece.id === editingId ? { ...piece, ...result.piece } : piece,
          ),
        );

        toast({
          title: "Success",
          description: "Piece updated successfully!",
        });
      } else {
        // Create new piece
        const response = await fetch("/api/pieces", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(pieceData),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || "Failed to create piece");
        }

        setPieces((prev) => [...prev, result.piece]);

        toast({
          title: "Success",
          description: "Piece added successfully!",
        });
      }

      resetForm();
      router.refresh();
    } catch (err) {
      console.error("Error saving piece:", err);
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred",
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (pieceId: string) => {
    if (!confirm("Are you sure you want to delete this piece?")) return;

    try {
      const response = await fetch(`/api/pieces/${pieceId}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to delete piece");
      }

      setPieces((prev) => prev.filter((piece) => piece.id !== pieceId));

      toast({
        title: "Success",
        description: "Piece deleted successfully!",
      });

      router.refresh();
    } catch (err) {
      console.error("Error deleting piece:", err);
      toast({
        title: "Error",
        description: "Failed to delete piece",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Add/Edit Form */}
      <Card>
        <CardHeader>
          <CardTitle>{editingId ? "Edit Piece" : "Add New Piece"}</CardTitle>
          <CardDescription>
            {editingId
              ? "Update the piece details"
              : "Add a piece to your project"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Piece Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, name: e.target.value }))
                  }
                  placeholder="e.g., Table Top, Leg, Rail"
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="quantity">Quantity *</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="1"
                  value={formData.quantity}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      quantity: e.target.value,
                    }))
                  }
                  required
                  disabled={loading}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="length">Length *</Label>
                <Input
                  id="length"
                  type="number"
                  step="0.1"
                  min="0"
                  value={formData.length}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, length: e.target.value }))
                  }
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="width">Width *</Label>
                <Input
                  id="width"
                  type="number"
                  step="0.1"
                  min="0"
                  value={formData.width}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, width: e.target.value }))
                  }
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="thickness">Thickness</Label>
                <Input
                  id="thickness"
                  type="number"
                  step="0.1"
                  min="0"
                  value={formData.thickness}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      thickness: e.target.value,
                    }))
                  }
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="unit">Unit</Label>
                <Select
                  value={formData.unit}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, unit: value }))
                  }
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MM">Millimeters (mm)</SelectItem>
                    <SelectItem value="CM">Centimeters (cm)</SelectItem>
                    <SelectItem value="IN">Inches (in)</SelectItem>
                    <SelectItem value="FT">Feet (ft)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="grainDirection">Grain Direction</Label>
                <Select
                  value={formData.grainDirection}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, grainDirection: value }))
                  }
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="NONE">No preference</SelectItem>
                    <SelectItem value="HORIZONTAL">Horizontal</SelectItem>
                    <SelectItem value="VERTICAL">Vertical</SelectItem>
                    <SelectItem value="EITHER">Either direction</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">Priority (1-10)</Label>
                <Input
                  id="priority"
                  type="number"
                  min="1"
                  max="10"
                  value={formData.priority}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      priority: e.target.value,
                    }))
                  }
                  disabled={loading}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, notes: e.target.value }))
                }
                placeholder="Optional notes about this piece"
                disabled={loading}
                rows={2}
              />
            </div>

            <div className="flex justify-end space-x-2">
              {editingId && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={resetForm}
                  disabled={loading}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={loading}>
                {loading ? (
                  "Saving..."
                ) : editingId ? (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Update Piece
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Piece
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Pieces List */}
      <Card>
        <CardHeader>
          <CardTitle>Project Pieces ({pieces.length})</CardTitle>
          <CardDescription>Manage the pieces for this project</CardDescription>
        </CardHeader>
        <CardContent>
          {pieces.length > 0 ? (
            <div className="space-y-4">
              {pieces.map((piece) => (
                <div
                  key={piece.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex-1">
                    <h4 className="font-medium">{piece.name}</h4>
                    <div className="text-sm text-gray-600 mt-1">
                      {piece.length} × {piece.width}
                      {piece.thickness && ` × ${piece.thickness}`} {piece.unit}
                      {piece.quantity > 1 && ` (×${piece.quantity})`}
                    </div>
                    {piece.grain_direction !== "NONE" && (
                      <div className="text-xs text-blue-600 mt-1">
                        Grain:{" "}
                        {piece.grain_direction &&
                          piece.grain_direction.toLowerCase()}
                      </div>
                    )}
                    {piece.notes && (
                      <div className="text-xs text-gray-500 mt-1">
                        {piece.notes}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">
                      Priority: {piece.priority}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(piece)}
                      disabled={loading}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(piece.id)}
                      disabled={loading}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Plus className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No pieces added yet</p>
              <p className="text-sm text-gray-500">
                Add pieces using the form above
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
