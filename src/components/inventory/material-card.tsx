"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Edit, Trash2, Copy } from "lucide-react";
import Link from "next/link";
import { formatNumber } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface Material {
  id: string;
  name: string;
  length: number;
  width: number;
  thickness: number | null;
  unit: string;
  quantity: number;
  cost: number | null;
  supplier: string | null;
  notes: string | null;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

interface MaterialCardProps {
  material: Material;
}

export function MaterialCard({ material }: MaterialCardProps) {
  const [deleting, setDeleting] = useState(false);
  const [cloning, setCloning] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleDeleteClick = () => {
    setShowDeleteDialog(true);
  };

  const handleDelete = async () => {
    setShowDeleteDialog(false);
    setDeleting(true);

    try {
      const response = await fetch(`/api/materials/${material.id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to delete material");
      }

      if (result.success) {
        toast({
          title: "Material deleted",
          description: "Your material has been deleted successfully.",
        });
        router.refresh();
      } else {
        throw new Error(result.error || "Failed to delete material");
      }
    } catch (err) {
      console.error("Error deleting material:", err);
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to delete material",
        variant: "destructive",
      });
    } finally {
      setDeleting(false);
    }
  };

  const handleClone = async () => {
    setCloning(true);

    try {
      const response = await fetch("/api/materials", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: `${material.name} (Copy)`,
          length: material.length,
          width: material.width,
          thickness: material.thickness,
          unit: material.unit,
          quantity: material.quantity,
          cost: material.cost,
          supplier: material.supplier,
          notes: material.notes,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to clone material");
      }

      if (result.success) {
        toast({
          title: "Material cloned",
          description: "Your material has been cloned successfully.",
        });
        router.refresh();
      } else {
        throw new Error(result.error || "Failed to clone material");
      }
    } catch (err) {
      console.error("Error cloning material:", err);
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to clone material",
        variant: "destructive",
      });
    } finally {
      setCloning(false);
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg">{material.name}</CardTitle>
            {material.supplier && (
              <CardDescription className="mt-1">
                Supplier: {material.supplier}
              </CardDescription>
            )}
          </div>
          <div className="flex space-x-1">
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/dashboard/inventory/${material.id}/edit`}>
                <Edit className="h-4 w-4" />
              </Link>
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleClone}
              disabled={cloning || deleting}
              title="Clone material"
            >
              <Copy className="h-4 w-4" />
            </Button>
            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
              <AlertDialogTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDeleteClick}
                  disabled={deleting || cloning}
                  title="Delete material"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete the material
                    "{material.name}" from your inventory.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    Delete Material
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {/* Dimensions */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Dimensions:</span>
              <span className="font-medium">
                {formatNumber(material.length)} × {formatNumber(material.width)}
                {material.thickness && ` × ${formatNumber(material.thickness)}`} {material.unit}
              </span>
            </div>
            
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Quantity:</span>
              <span className="font-medium">{material.quantity}</span>
            </div>

            {material.cost && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Cost per unit:</span>
                <span className="font-medium">${formatNumber(material.cost)}</span>
              </div>
            )}

            {material.cost && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Total value:</span>
                <span className="font-medium">${formatNumber(material.cost * material.quantity)}</span>
              </div>
            )}
          </div>

          {/* Notes */}
          {material.notes && (
            <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
              {material.notes}
            </div>
          )}

          {/* Area calculation */}
          <div className="text-xs text-gray-500 pt-2 border-t">
            Area: {formatNumber((material.length * material.width) / 1000000)} m² per piece
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
