import { auth } from '@/lib/auth'
import { db } from '@/lib/database'
import { projects, pieces } from '@/lib/schema'
import { eq, desc, count } from 'drizzle-orm'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, FolderOpen, Calendar, Settings, Trash2 } from 'lucide-react'
import Link from 'next/link'
import { formatDate } from '@/lib/utils'
import { redirect } from 'next/navigation'
import { headers } from 'next/headers'

export default async function ProjectsPage() {
  // Get current session
  const session = await auth.api.getSession({
    headers: await headers()
  })

  if (!session) {
    redirect('/auth/login')
  }

  // Initialize default values
  let userProjects: any[] = []
  let error = null

  // Get user's projects with piece counts
  try {
    const projectsWithPieces = await db
      .select({
        id: projects.id,
        name: projects.name,
        description: projects.description,
        sawKerf: projects.sawKerf,
        kerfUnit: projects.kerfUnit,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
        pieceCount: count(pieces.id)
      })
      .from(projects)
      .leftJoin(pieces, eq(projects.id, pieces.projectId))
      .where(eq(projects.userId, session.user.id))
      .groupBy(projects.id)
      .orderBy(desc(projects.updatedAt))

    userProjects = projectsWithPieces || []
  } catch (err) {
    console.error('Error fetching projects:', err)
    error = err
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Projects</h1>
          <p className="text-gray-600 mt-2">
            Manage your woodworking projects and cutting plans
          </p>
        </div>
        <Button asChild>
          <Link href="/dashboard/projects/new">
            <Plus className="h-4 w-4 mr-2" />
            New Project
          </Link>
        </Button>
      </div>

      {/* Projects Grid */}
      {userProjects && userProjects.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {userProjects.map((project) => (
            <Card key={project.id} className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{project.name}</CardTitle>
                    {project.description && (
                      <CardDescription className="mt-2">
                        {project.description}
                      </CardDescription>
                    )}
                  </div>
                  <div className="flex space-x-1">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/dashboard/projects/${project.id}/settings`}>
                        <Settings className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Project Stats */}
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Pieces:</span>
                    <span className="font-medium">{project.pieceCount || 0}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Saw Kerf:</span>
                    <span className="font-medium">{project.sawKerf}{project.kerfUnit}</span>
                  </div>

                  {/* Dates */}
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>Updated {formatDate(new Date(project.updatedAt))}</span>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2 pt-2">
                    <Button asChild className="flex-1">
                      <Link href={`/dashboard/projects/${project.id}`}>
                        <FolderOpen className="h-4 w-4 mr-2" />
                        Open
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        /* Empty State */
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-16">
            <FolderOpen className="h-16 w-16 text-gray-400 mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No projects yet</h3>
            <p className="text-gray-600 text-center mb-6 max-w-md">
              Get started by creating your first woodworking project. You can add materials, 
              pieces, and generate optimized cutting plans.
            </p>
            <Button asChild>
              <Link href="/dashboard/projects/new">
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Project
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      {userProjects && userProjects.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Projects</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{userProjects.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Pieces</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {userProjects.reduce((sum, project) => sum + (project.pieceCount || 0), 0)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-600">
                {userProjects[0] ? `Last updated: ${formatDate(new Date(userProjects[0].updatedAt))}` : 'No activity'}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
