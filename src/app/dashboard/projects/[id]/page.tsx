import { auth } from '@/lib/auth'
import { db } from '@/lib/database'
import { projects, pieces, materials, optimizationResults, sheets, placedPieces } from '@/lib/schema'
import { eq, desc } from 'drizzle-orm'
import { notFound, redirect } from 'next/navigation'
import { ProjectDetailClient } from '@/components/projects/project-detail-client'
import { headers } from 'next/headers'

interface ProjectDetailPageProps {
  params: {
    id: string
  }
}

export default async function ProjectDetailPage({ params }: ProjectDetailPageProps) {
  // Get current session
  const session = await auth.api.getSession({
    headers: await headers()
  })

  if (!session) {
    redirect('/auth/login')
  }

  // Initialize default values
  let project: any = null
  let userMaterials: any[] = []
  let latestOptimizationResults: any[] = []

  try {
    // Get project with pieces
    const projectData = await db.query.projects.findFirst({
      where: eq(projects.id, params.id),
      with: {
        pieces: true
      }
    })

    if (!projectData || projectData.userId !== session.user.id) {
      notFound()
    }

    project = {
      id: projectData.id,
      name: projectData.name,
      description: projectData.description,
      saw_kerf: projectData.sawKerf,
      kerf_unit: projectData.kerfUnit,
      pieces: projectData.pieces.map(piece => ({
        id: piece.id,
        name: piece.name,
        length: piece.length,
        width: piece.width,
        thickness: piece.thickness,
        unit: piece.unit,
        quantity: piece.quantity,
        grain_direction: piece.grainDirection,
        priority: piece.priority,
        notes: piece.notes
      }))
    }

    // Get user's materials for optimization
    const materialsData = await db.query.materials.findMany({
      where: eq(materials.userId, session.user.id),
      orderBy: [materials.name]
    })

    userMaterials = materialsData || []

    // Get recent optimization results for this project
    const optimizationData = await db.query.optimizationResults.findMany({
      where: eq(optimizationResults.projectId, params.id),
      with: {
        sheets: {
          with: {
            placedPieces: true
          }
        }
      },
      orderBy: [desc(optimizationResults.createdAt)],
      limit: 1
    })

    latestOptimizationResults = optimizationData || []
  } catch (err) {
    console.error('Error fetching project data:', err)
    notFound()
  }

  return (
    <ProjectDetailClient
      project={project}
      materials={userMaterials || []}
      initialOptimizationResult={latestOptimizationResults?.[0] || null}
    />
  )
}
