import { auth } from '@/lib/auth'
import { db } from '@/lib/database'
import { projects } from '@/lib/schema'
import { eq, and } from 'drizzle-orm'
import { notFound, redirect } from 'next/navigation'
import { ProjectSettings } from '@/components/projects/project-settings'
import { headers } from 'next/headers'

interface ProjectSettingsPageProps {
  params: {
    id: string
  }
}

export default async function ProjectSettingsPage({ params }: ProjectSettingsPageProps) {
  // Get current session
  const session = await auth.api.getSession({
    headers: await headers()
  })

  if (!session) {
    redirect('/auth/login')
  }

  // Get project
  const projectData = await db.query.projects.findFirst({
    where: and(
      eq(projects.id, params.id),
      eq(projects.userId, session.user.id)
    ),
  })

  if (!projectData) {
    notFound()
  }

  // Transform to match expected interface
  const project = {
    id: projectData.id,
    name: projectData.name,
    description: projectData.description,
    saw_kerf: projectData.sawKerf,
    kerf_unit: projectData.kerfUnit,
  }

  return (
    <ProjectSettings project={project} />
  )
}
