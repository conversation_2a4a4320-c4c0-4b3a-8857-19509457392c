import { auth } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { MaterialForm } from '@/components/inventory/material-form'
import { headers } from 'next/headers'

export default async function NewMaterialPage() {
  // Get current session
  const session = await auth.api.getSession({
    headers: await headers()
  })

  if (!session) {
    redirect('/auth/login')
  }

  return (
    <MaterialForm />
  )
}
