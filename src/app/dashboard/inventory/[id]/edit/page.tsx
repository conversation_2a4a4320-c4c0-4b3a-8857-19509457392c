import { auth } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { db } from '@/lib/database'
import { materials } from '@/lib/schema'
import { eq, and } from 'drizzle-orm'
import { MaterialForm } from '@/components/inventory/material-form'
import { headers } from 'next/headers'
import { notFound } from 'next/navigation'

interface EditMaterialPageProps {
  params: {
    id: string
  }
}

export default async function EditMaterialPage({ params }: EditMaterialPageProps) {
  // Get current session
  const session = await auth.api.getSession({
    headers: await headers()
  })

  if (!session) {
    redirect('/auth/login')
  }

  // Get the material to edit
  const material = await db.query.materials.findFirst({
    where: and(
      eq(materials.id, params.id),
      eq(materials.userId, session.user.id)
    )
  })

  if (!material) {
    notFound()
  }

  return (
    <MaterialForm material={material} />
  )
}
