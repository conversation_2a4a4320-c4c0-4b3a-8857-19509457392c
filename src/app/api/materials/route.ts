import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/database";
import { materials } from "@/lib/schema";
import { eq, desc } from "drizzle-orm";
import { z } from "zod";
import { nanoid } from "nanoid";

// Validation schema for material creation
const CreateMaterialSchema = z.object({
  name: z.string().min(1, "Material name is required"),
  length: z.number().positive("Length must be positive"),
  width: z.number().positive("Width must be positive"),
  thickness: z.number().positive().optional().default(18),
  unit: z.enum(["MM", "CM", "M", "IN", "FT"]).default("MM"),
  quantity: z.number().int().positive().default(1),
  grainDirection: z.enum(["NONE", "HORIZONTAL", "VERTICAL", "EITHER"]).default("NONE"),
  cost: z.number().min(0).optional().default(1),
  supplier: z.string().optional().default("Unknown"),
  notes: z.string().optional().nullable(),
});

// GET /api/materials - Get user's materials
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    // Get user's materials
    const userMaterials = await db.query.materials.findMany({
      where: eq(materials.userId, session.user.id),
      orderBy: [desc(materials.updatedAt)],
    });

    return NextResponse.json({
      success: true,
      materials: userMaterials,
    });
  } catch (error) {
    console.error("Error fetching materials:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// POST /api/materials - Create a new material
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = CreateMaterialSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid request data",
          details: validationResult.error.errors,
        },
        { status: 400 },
      );
    }

    const materialData = validationResult.data;

    // Create new material
    const newMaterial = await db
      .insert(materials)
      .values({
        id: nanoid(),
        name: materialData.name,
        length: materialData.length,
        width: materialData.width,
        thickness: materialData.thickness || null,
        unit: materialData.unit,
        quantity: materialData.quantity,
        grainDirection: materialData.grainDirection,
        cost: materialData.cost || null,
        supplier: materialData.supplier || null,
        notes: materialData.notes || null,
        userId: session.user.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return NextResponse.json({
      success: true,
      material: newMaterial[0],
    });
  } catch (error) {
    console.error("Error creating material:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
