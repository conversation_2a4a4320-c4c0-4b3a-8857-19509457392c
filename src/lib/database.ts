import { drizzle } from 'drizzle-orm/node-postgres';
import { Client } from 'pg';
import * as schema from './schema';

export const client = new Client({
  connectionString: process.env.DATABASE_URL!,
});

// Global connection for development
const globalForDb = globalThis as unknown as {
  client: Client | undefined;
  db: ReturnType<typeof drizzle> | undefined;
};

// Initialize client connection
if (!globalForDb.client) {
  globalForDb.client = client;
  // Connect the client
  client.connect().catch(console.error);
}

// Create Drizzle instance with schema for relational queries
export const db = globalForDb.db ?? drizzle(globalForDb.client!, { schema });

if (process.env.NODE_ENV !== 'production') {
  globalForDb.db = db;
}
