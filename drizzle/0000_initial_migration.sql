-- Create enums
DO $$ BEGIN
 CREATE TYPE "public"."UserPlan" AS ENUM('FREE', 'PRO', 'ENTERPRISE');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "public"."Unit" AS ENUM('MM', 'CM', 'M', 'IN', 'FT');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "public"."GrainDirection" AS ENUM('NONE', 'HORIZONTAL', 'VERTICAL', 'EITHER');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Better-Auth tables (from existing migration)
CREATE TABLE IF NOT EXISTS "user" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"emailVerified" boolean NOT NULL,
	"image" text,
	"createdAt" timestamp NOT NULL,
	"updatedAt" timestamp NOT NULL,
	CONSTRAINT "user_email_unique" UNIQUE("email")
);

CREATE TABLE IF NOT EXISTS "session" (
	"id" text PRIMARY KEY NOT NULL,
	"expiresAt" timestamp NOT NULL,
	"token" text NOT NULL,
	"createdAt" timestamp NOT NULL,
	"updatedAt" timestamp NOT NULL,
	"ipAddress" text,
	"userAgent" text,
	"userId" text NOT NULL,
	CONSTRAINT "session_token_unique" UNIQUE("token")
);

CREATE TABLE IF NOT EXISTS "account" (
	"id" text PRIMARY KEY NOT NULL,
	"accountId" text NOT NULL,
	"providerId" text NOT NULL,
	"userId" text NOT NULL,
	"accessToken" text,
	"refreshToken" text,
	"idToken" text,
	"accessTokenExpiresAt" timestamp,
	"refreshTokenExpiresAt" timestamp,
	"scope" text,
	"password" text,
	"createdAt" timestamp NOT NULL,
	"updatedAt" timestamp NOT NULL
);

CREATE TABLE IF NOT EXISTS "verification" (
	"id" text PRIMARY KEY NOT NULL,
	"identifier" text NOT NULL,
	"value" text NOT NULL,
	"expiresAt" timestamp NOT NULL,
	"createdAt" timestamp,
	"updatedAt" timestamp
);

-- Application tables
CREATE TABLE IF NOT EXISTS "users" (
	"id" text PRIMARY KEY NOT NULL,
	"email" text NOT NULL,
	"username" text,
	"name" text,
	"avatar" text,
	"plan" "UserPlan" DEFAULT 'FREE',
	"password" text NOT NULL,
	"emailVerified" timestamp,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "users_email_unique" UNIQUE("email"),
	CONSTRAINT "users_username_unique" UNIQUE("username")
);

CREATE TABLE IF NOT EXISTS "projects" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"sawKerf" real DEFAULT 3 NOT NULL,
	"kerfUnit" "Unit" DEFAULT 'MM' NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"userId" text NOT NULL
);

CREATE TABLE IF NOT EXISTS "materials" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"length" real NOT NULL,
	"width" real NOT NULL,
	"thickness" real,
	"unit" "Unit" DEFAULT 'MM' NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"cost" real,
	"supplier" text,
	"notes" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"userId" text NOT NULL
);

CREATE TABLE IF NOT EXISTS "pieces" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"length" real NOT NULL,
	"width" real NOT NULL,
	"thickness" real,
	"unit" "Unit" DEFAULT 'MM' NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"grainDirection" "GrainDirection" DEFAULT 'NONE' NOT NULL,
	"priority" integer DEFAULT 1 NOT NULL,
	"notes" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"projectId" text NOT NULL
);

CREATE TABLE IF NOT EXISTS "optimization_results" (
	"id" text PRIMARY KEY NOT NULL,
	"algorithm" text NOT NULL,
	"efficiency" real NOT NULL,
	"wastePercentage" real NOT NULL,
	"totalSheets" integer NOT NULL,
	"totalCost" real,
	"processingTime" integer NOT NULL,
	"metadata" json,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"projectId" text NOT NULL,
	"userId" text NOT NULL
);

CREATE TABLE IF NOT EXISTS "optimization_materials" (
	"id" text PRIMARY KEY NOT NULL,
	"quantity" integer NOT NULL,
	"materialSnapshot" json NOT NULL,
	"optimizationResultId" text NOT NULL,
	"materialId" text NOT NULL
);

CREATE TABLE IF NOT EXISTS "sheets" (
	"id" text PRIMARY KEY NOT NULL,
	"sheetIndex" integer NOT NULL,
	"widthUsed" real NOT NULL,
	"heightUsed" real NOT NULL,
	"materialSnapshot" json NOT NULL,
	"optimizationResultId" text NOT NULL
);

CREATE TABLE IF NOT EXISTS "placed_pieces" (
	"id" text PRIMARY KEY NOT NULL,
	"x" real NOT NULL,
	"y" real NOT NULL,
	"packedWidth" real NOT NULL,
	"packedHeight" real NOT NULL,
	"rotation" real DEFAULT 0 NOT NULL,
	"color" text,
	"pieceSnapshot" json NOT NULL,
	"sheetId" text NOT NULL,
	"pieceId" text NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "session" ADD CONSTRAINT "session_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "account" ADD CONSTRAINT "account_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "projects" ADD CONSTRAINT "projects_userId_users_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "materials" ADD CONSTRAINT "materials_userId_users_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "pieces" ADD CONSTRAINT "pieces_projectId_projects_id_fk" FOREIGN KEY ("projectId") REFERENCES "public"."projects"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "optimization_results" ADD CONSTRAINT "optimization_results_projectId_projects_id_fk" FOREIGN KEY ("projectId") REFERENCES "public"."projects"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "optimization_results" ADD CONSTRAINT "optimization_results_userId_users_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "optimization_materials" ADD CONSTRAINT "optimization_materials_optimizationResultId_optimization_results_id_fk" FOREIGN KEY ("optimizationResultId") REFERENCES "public"."optimization_results"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "optimization_materials" ADD CONSTRAINT "optimization_materials_materialId_materials_id_fk" FOREIGN KEY ("materialId") REFERENCES "public"."materials"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "sheets" ADD CONSTRAINT "sheets_optimizationResultId_optimization_results_id_fk" FOREIGN KEY ("optimizationResultId") REFERENCES "public"."optimization_results"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "placed_pieces" ADD CONSTRAINT "placed_pieces_sheetId_sheets_id_fk" FOREIGN KEY ("sheetId") REFERENCES "public"."sheets"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "placed_pieces" ADD CONSTRAINT "placed_pieces_pieceId_pieces_id_fk" FOREIGN KEY ("pieceId") REFERENCES "public"."pieces"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
