{"id": "4ce7ac2b-1380-4cab-a0ff-c9cbf5cd5364", "prevId": "69734d77-88b4-4ac0-9ddb-f30f40b44e22", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true}, "providerId": {"name": "providerId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "accessToken": {"name": "accessToken", "type": "text", "primaryKey": false, "notNull": false}, "refreshToken": {"name": "refreshToken", "type": "text", "primaryKey": false, "notNull": false}, "idToken": {"name": "idToken", "type": "text", "primaryKey": false, "notNull": false}, "accessTokenExpiresAt": {"name": "accessTokenExpiresAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "refreshTokenExpiresAt": {"name": "refreshTokenExpiresAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_userId_user_id_fk": {"name": "account_userId_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.materials": {"name": "materials", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "length": {"name": "length", "type": "real", "primaryKey": false, "notNull": true}, "width": {"name": "width", "type": "real", "primaryKey": false, "notNull": true}, "thickness": {"name": "thickness", "type": "real", "primaryKey": false, "notNull": false}, "unit": {"name": "unit", "type": "Unit", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'MM'"}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "grainDirection": {"name": "grainDirection", "type": "GrainDirection", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'NONE'"}, "cost": {"name": "cost", "type": "real", "primaryKey": false, "notNull": false}, "supplier": {"name": "supplier", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"materials_userId_user_id_fk": {"name": "materials_userId_user_id_fk", "tableFrom": "materials", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.optimization_materials": {"name": "optimization_materials", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true}, "materialSnapshot": {"name": "materialSnapshot", "type": "json", "primaryKey": false, "notNull": true}, "optimizationResultId": {"name": "optimizationResultId", "type": "text", "primaryKey": false, "notNull": true}, "materialId": {"name": "materialId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"optimization_materials_optimizationResultId_optimization_results_id_fk": {"name": "optimization_materials_optimizationResultId_optimization_results_id_fk", "tableFrom": "optimization_materials", "tableTo": "optimization_results", "columnsFrom": ["optimizationResultId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "optimization_materials_materialId_materials_id_fk": {"name": "optimization_materials_materialId_materials_id_fk", "tableFrom": "optimization_materials", "tableTo": "materials", "columnsFrom": ["materialId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.optimization_results": {"name": "optimization_results", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "algorithm": {"name": "algorithm", "type": "text", "primaryKey": false, "notNull": true}, "efficiency": {"name": "efficiency", "type": "real", "primaryKey": false, "notNull": true}, "wastePercentage": {"name": "wastePercentage", "type": "real", "primaryKey": false, "notNull": true}, "totalSheets": {"name": "totalSheets", "type": "integer", "primaryKey": false, "notNull": true}, "totalCost": {"name": "totalCost", "type": "real", "primaryKey": false, "notNull": false}, "processingTime": {"name": "processingTime", "type": "integer", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "projectId": {"name": "projectId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"optimization_results_projectId_projects_id_fk": {"name": "optimization_results_projectId_projects_id_fk", "tableFrom": "optimization_results", "tableTo": "projects", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "optimization_results_userId_user_id_fk": {"name": "optimization_results_userId_user_id_fk", "tableFrom": "optimization_results", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pieces": {"name": "pieces", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "length": {"name": "length", "type": "real", "primaryKey": false, "notNull": true}, "width": {"name": "width", "type": "real", "primaryKey": false, "notNull": true}, "thickness": {"name": "thickness", "type": "real", "primaryKey": false, "notNull": false}, "unit": {"name": "unit", "type": "Unit", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'MM'"}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "grainDirection": {"name": "grainDirection", "type": "GrainDirection", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'NONE'"}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "projectId": {"name": "projectId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"pieces_projectId_projects_id_fk": {"name": "pieces_projectId_projects_id_fk", "tableFrom": "pieces", "tableTo": "projects", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.placed_pieces": {"name": "placed_pieces", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "x": {"name": "x", "type": "real", "primaryKey": false, "notNull": true}, "y": {"name": "y", "type": "real", "primaryKey": false, "notNull": true}, "packedWidth": {"name": "packedWidth", "type": "real", "primaryKey": false, "notNull": true}, "packedHeight": {"name": "packedHeight", "type": "real", "primaryKey": false, "notNull": true}, "rotation": {"name": "rotation", "type": "real", "primaryKey": false, "notNull": true, "default": 0}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false}, "pieceSnapshot": {"name": "pieceSnapshot", "type": "json", "primaryKey": false, "notNull": true}, "sheetId": {"name": "sheetId", "type": "text", "primaryKey": false, "notNull": true}, "pieceId": {"name": "pieceId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"placed_pieces_sheetId_sheets_id_fk": {"name": "placed_pieces_sheetId_sheets_id_fk", "tableFrom": "placed_pieces", "tableTo": "sheets", "columnsFrom": ["sheetId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "placed_pieces_pieceId_pieces_id_fk": {"name": "placed_pieces_pieceId_pieces_id_fk", "tableFrom": "placed_pieces", "tableTo": "pieces", "columnsFrom": ["pieceId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "sawKerf": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "real", "primaryKey": false, "notNull": true, "default": 3}, "kerfUnit": {"name": "kerfUnit", "type": "Unit", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'MM'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"projects_userId_user_id_fk": {"name": "projects_userId_user_id_fk", "tableFrom": "projects", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "userAgent": {"name": "userAgent", "type": "text", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_userId_user_id_fk": {"name": "session_userId_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sheets": {"name": "sheets", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "sheetIndex": {"name": "sheetIndex", "type": "integer", "primaryKey": false, "notNull": true}, "widthUsed": {"name": "widthUsed", "type": "real", "primaryKey": false, "notNull": true}, "heightUsed": {"name": "heightUsed", "type": "real", "primaryKey": false, "notNull": true}, "materialSnapshot": {"name": "materialSnapshot", "type": "json", "primaryKey": false, "notNull": true}, "optimizationResultId": {"name": "optimizationResultId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sheets_optimizationResultId_optimization_results_id_fk": {"name": "sheets_optimizationResultId_optimization_results_id_fk", "tableFrom": "sheets", "tableTo": "optimization_results", "columnsFrom": ["optimizationResultId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "emailVerified": {"name": "emailVerified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.GrainDirection": {"name": "GrainDirection", "schema": "public", "values": ["NONE", "HORIZONTAL", "VERTICAL", "EITHER"]}, "public.Unit": {"name": "Unit", "schema": "public", "values": ["MM", "CM", "M", "IN", "FT"]}, "public.UserPlan": {"name": "UserPlan", "schema": "public", "values": ["FREE", "PRO", "ENTERPRISE"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}