CREATE TYPE "public"."GrainDirection" AS ENUM('NONE', 'HORIZONTAL', 'VERTICAL', 'EITHER');--> statement-breakpoint
CREATE TYPE "public"."Unit" AS ENUM('MM', 'CM', 'M', 'IN', 'FT');--> statement-breakpoint
CREATE TYPE "public"."UserPlan" AS ENUM('FREE', 'PRO', 'ENTERPRISE');--> statement-breakpoint
CREATE TABLE "account" (
	"id" text PRIMARY KEY NOT NULL,
	"accountId" text NOT NULL,
	"providerId" text NOT NULL,
	"userId" text NOT NULL,
	"accessToken" text,
	"refreshToken" text,
	"idToken" text,
	"accessTokenExpiresAt" timestamp,
	"refreshTokenExpiresAt" timestamp,
	"scope" text,
	"password" text,
	"createdAt" timestamp NOT NULL,
	"updatedAt" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "materials" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"length" real NOT NULL,
	"width" real NOT NULL,
	"thickness" real,
	"unit" "Unit" DEFAULT 'MM' NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"cost" real,
	"supplier" text,
	"notes" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"userId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "optimization_materials" (
	"id" text PRIMARY KEY NOT NULL,
	"quantity" integer NOT NULL,
	"materialSnapshot" json NOT NULL,
	"optimizationResultId" text NOT NULL,
	"materialId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "optimization_results" (
	"id" text PRIMARY KEY NOT NULL,
	"algorithm" text NOT NULL,
	"efficiency" real NOT NULL,
	"wastePercentage" real NOT NULL,
	"totalSheets" integer NOT NULL,
	"totalCost" real,
	"processingTime" integer NOT NULL,
	"metadata" json,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"projectId" text NOT NULL,
	"userId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "pieces" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"length" real NOT NULL,
	"width" real NOT NULL,
	"thickness" real,
	"unit" "Unit" DEFAULT 'MM' NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"grainDirection" "GrainDirection" DEFAULT 'NONE' NOT NULL,
	"priority" integer DEFAULT 1 NOT NULL,
	"notes" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"projectId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "placed_pieces" (
	"id" text PRIMARY KEY NOT NULL,
	"x" real NOT NULL,
	"y" real NOT NULL,
	"packedWidth" real NOT NULL,
	"packedHeight" real NOT NULL,
	"rotation" real DEFAULT 0 NOT NULL,
	"color" text,
	"pieceSnapshot" json NOT NULL,
	"sheetId" text NOT NULL,
	"pieceId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "projects" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"sawKerf" real DEFAULT 3 NOT NULL,
	"kerfUnit" "Unit" DEFAULT 'MM' NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"userId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "session" (
	"id" text PRIMARY KEY NOT NULL,
	"expiresAt" timestamp NOT NULL,
	"token" text NOT NULL,
	"createdAt" timestamp NOT NULL,
	"updatedAt" timestamp NOT NULL,
	"ipAddress" text,
	"userAgent" text,
	"userId" text NOT NULL,
	CONSTRAINT "session_token_unique" UNIQUE("token")
);
--> statement-breakpoint
CREATE TABLE "sheets" (
	"id" text PRIMARY KEY NOT NULL,
	"sheetIndex" integer NOT NULL,
	"widthUsed" real NOT NULL,
	"heightUsed" real NOT NULL,
	"materialSnapshot" json NOT NULL,
	"optimizationResultId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"emailVerified" boolean NOT NULL,
	"image" text,
	"createdAt" timestamp NOT NULL,
	"updatedAt" timestamp NOT NULL,
	CONSTRAINT "user_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "verification" (
	"id" text PRIMARY KEY NOT NULL,
	"identifier" text NOT NULL,
	"value" text NOT NULL,
	"expiresAt" timestamp NOT NULL,
	"createdAt" timestamp,
	"updatedAt" timestamp
);
--> statement-breakpoint
ALTER TABLE "account" ADD CONSTRAINT "account_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "materials" ADD CONSTRAINT "materials_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "optimization_materials" ADD CONSTRAINT "optimization_materials_optimizationResultId_optimization_results_id_fk" FOREIGN KEY ("optimizationResultId") REFERENCES "public"."optimization_results"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "optimization_materials" ADD CONSTRAINT "optimization_materials_materialId_materials_id_fk" FOREIGN KEY ("materialId") REFERENCES "public"."materials"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "optimization_results" ADD CONSTRAINT "optimization_results_projectId_projects_id_fk" FOREIGN KEY ("projectId") REFERENCES "public"."projects"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "optimization_results" ADD CONSTRAINT "optimization_results_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pieces" ADD CONSTRAINT "pieces_projectId_projects_id_fk" FOREIGN KEY ("projectId") REFERENCES "public"."projects"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "placed_pieces" ADD CONSTRAINT "placed_pieces_sheetId_sheets_id_fk" FOREIGN KEY ("sheetId") REFERENCES "public"."sheets"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "placed_pieces" ADD CONSTRAINT "placed_pieces_pieceId_pieces_id_fk" FOREIGN KEY ("pieceId") REFERENCES "public"."pieces"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "projects" ADD CONSTRAINT "projects_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "session" ADD CONSTRAINT "session_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sheets" ADD CONSTRAINT "sheets_optimizationResultId_optimization_results_id_fk" FOREIGN KEY ("optimizationResultId") REFERENCES "public"."optimization_results"("id") ON DELETE cascade ON UPDATE no action;