# Woodworking Cut Optimizer

A professional full-stack Next.js application for optimizing woodworking cuts with advanced algorithms, user authentication, and comprehensive project management.

## 🚀 **New Full-Stack Architecture**

This application has been completely refactored from a client-only tool to a modern full-stack solution:

- **Next.js 14** with App Router and TypeScript
- **PostgreSQL** database with Drizzle ORM
- **Better-Auth** authentication system
- **Server-side optimization** algorithms (protected IP)
- **shadcn/ui** component library with Tailwind CSS
- **Interactive d3.js** visualizations
- **Responsive design** for all devices

## ✨ **Key Features**

### 🔐 **Authentication & Security**
- User registration and login with email verification
- Secure password requirements and validation
- Protected routes and user session management
- Server-side optimization algorithms (IP protection)

### 📁 **Project Management**
- Create and organize multiple woodworking projects
- Project-specific settings (saw kerf, units, descriptions)
- Edit and delete projects with confirmation
- Project dashboard with recent activity

### 🔧 **Material & Piece Management**
- **Add Materials**: Define base materials with dimensions, quantities, and costs
- **Add Pieces**: Create project pieces with grain direction preferences
- **Edit Functionality**: Modify existing materials and pieces
- **Inventory Management**: Track material costs and suppliers

### ⚡ **Advanced Optimization**
- **Server-side algorithms** with multiple optimization strategies
- **Proprietary bin packing** algorithms (protected intellectual property)
- **Multi-strategy optimization** (bottom-left fill, best-fit decreasing, guillotine split, etc.)
- **Efficiency calculations** and waste minimization
- **Real-time optimization** with progress tracking

### 📊 **Interactive Visualization**
- **d3.js powered** cutting layout visualization
- **Clickable pieces** with hover effects and selection highlighting
- **Interactive navigation** between multiple sheets
- **Piece information panels** with dimensions and details
- **Print-friendly layouts** with proper scaling

### 🖨️ **Export & Print**
- **Complete plan export** (HTML with visualizations)
- **Cutting list export** (text format)
- **Print optimization** with separate pages for cutting list and layouts
- **Professional formatting** for workshop use

### 🎨 **Modern UI/UX**
- **Responsive design** works on desktop, tablet, and mobile
- **Dark/light mode** support (coming soon)
- **Sidebar navigation** with collapsible menu
- **Tabbed interface** for cutting list and visualization
- **Loading states** and error handling
- **Toast notifications** for user feedback

## 🛠️ **Technology Stack**

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Modern component library
- **Radix UI** - Accessible component primitives
- **d3.js** - Data visualization library
- **Lucide React** - Beautiful icons

### Backend
- **Next.js API Routes** - Server-side API endpoints
- **Drizzle ORM** - Type-safe database client
- **PostgreSQL** - Robust relational database
- **Better-Auth** - Authentication system
- **Zod** - Runtime type validation
- **bcryptjs** - Password hashing

### Development Tools
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **TypeScript** - Static type checking
- **Drizzle Studio** - Database management GUI

## 🚀 **Quick Start**

### Prerequisites
- Node.js 18+
- PostgreSQL database

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd woodworking-optimizer
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your database credentials
   ```

4. **Set up the database:**
   ```bash
   npm run db:generate
   npm run db:migrate:run
   npm run auth:generate
   npm run auth:migrate
   ```

5. **Start the development server:**
   ```bash
   npm run dev
   ```

6. **Open your browser:**
   Navigate to `http://localhost:3000`

For detailed setup instructions, see [SETUP_GUIDE.md](./SETUP_GUIDE.md)

## 📖 **Usage**

### Getting Started
1. **Register an account** or sign in
2. **Create your first project** from the dashboard
3. **Add materials** to your inventory
4. **Add pieces** to your project with dimensions
5. **Run optimization** to generate cutting plans
6. **View interactive visualization** of the layout
7. **Print or export** your cutting plan

### Project Workflow
1. **Dashboard** - Overview of projects and recent activity
2. **Projects** - Create and manage woodworking projects
3. **Inventory** - Manage materials and track costs
4. **Optimization** - Generate efficient cutting plans
5. **Visualization** - Interactive layout viewing
6. **Export** - Print-ready cutting plans

## 🔒 **Security & IP Protection**

### Intellectual Property Protection
- **Server-side optimization** algorithms never exposed to client
- **Proprietary bin packing** strategies protected on server
- **API rate limiting** to prevent abuse
- **Authentication required** for all optimization requests

### Data Security
- **Session-based authentication** with Better-Auth
- **User data isolation** - users can only access their own data
- **Secure authentication** with email/password and social providers
- **Input validation** and sanitization
- **HTTPS encryption** in production

## 🔄 **Authentication Migration: Supabase → Better-Auth**

This application has been migrated from Supabase authentication to Better-Auth for improved flexibility and control:

### Migration Benefits
- **Framework-agnostic** authentication solution
- **Better TypeScript** support and type safety
- **More flexible** session management
- **Easier customization** and plugin system
- **Reduced vendor lock-in**

### Setup Instructions
1. **Install dependencies**: `npm install better-auth pg @types/pg`
2. **Generate auth schema**: `npm run auth:generate`
3. **Run migrations**: `npm run auth:migrate`
4. **Update environment variables** (see `.env.local` example)

## 🎯 **Migration from Legacy Version**

If you're upgrading from the previous client-only version:

1. **Data Export**: Export your projects and materials from localStorage
2. **Account Creation**: Register for a new account
3. **Data Import**: Manually recreate projects and materials
4. **Feature Verification**: Test all functionality in the new system

The new version provides significant improvements:
- **Better performance** with server-side optimization
- **Data persistence** across devices and sessions
- **Enhanced security** and user management
- **Professional UI/UX** with modern design patterns

## 🤝 **Contributing**

We welcome contributions! Please see our contributing guidelines:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Development Guidelines
- Follow TypeScript best practices
- Use Prettier for code formatting
- Write meaningful commit messages
- Test your changes thoroughly

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 **Support**

- **Documentation**: Check [SETUP_GUIDE.md](./SETUP_GUIDE.md) for detailed setup
- **Issues**: Report bugs and feature requests on GitHub
- **Discussions**: Join community discussions for help and ideas

## 🗺️ **Roadmap**

### Upcoming Features
- **Collaboration tools** - Share projects with team members
- **Advanced reporting** - Detailed analytics and insights
- **Mobile app** - Native iOS and Android applications
- **API integrations** - Connect with inventory management systems
- **Advanced materials** - Support for complex material properties
- **Cost optimization** - Factor in material costs for optimization

### Performance Improvements
- **Caching layer** - Redis for improved performance
- **Background processing** - Queue system for large optimizations
- **Real-time updates** - WebSocket support for live collaboration

---

**Built with ❤️ for the woodworking community**
